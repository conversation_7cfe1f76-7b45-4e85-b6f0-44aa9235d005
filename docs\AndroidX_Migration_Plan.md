# AndroidX 迁移实施方案

## 项目背景

华智公考 Android 项目当前使用 Android Support Library，但友盟SDK升级要求使用 AndroidX。本文档详细说明 AndroidX 迁移的实施方案。

## 当前项目状态

### 技术栈
- **compileSdkVersion**: 33
- **minSdkVersion**: 21  
- **targetSdkVersion**: 33
- **Gradle Plugin**: 7.3.1
- **Kotlin**: 1.8.0

### 主要 Support Library 依赖
```gradle
implementation 'com.android.support:multidex:1.0.3'
implementation 'com.android.support:design:27.1.1'
implementation 'com.android.support:appcompat-v7:27.1.1'
implementation 'com.android.support.constraint:constraint-layout:1.1.3'
implementation 'com.android.support:support-v4:27.1.1'
```

### 涉及模块
- **主模块**: Motoon
- **子模块**: commonui, kf5sdkModule, StickyListHeaders, ePub-Library, AndroidCrop, Android-ViewPagerIndicator, PagerSlidingTabStrip

## 迁移方案

### 阶段一：准备工作（1天）

#### 1.1 环境准备
- [ ] 创建迁移专用分支 `feature/androidx-migration`
- [ ] 备份当前稳定版本代码
- [ ] 确保 Gradle 版本兼容性

#### 1.2 依赖分析
- [ ] 分析所有第三方库的 AndroidX 兼容性
- [ ] 制定第三方库升级计划
- [ ] 识别需要手动处理的自定义组件

### 阶段二：自动迁移（2天）

#### 2.1 使用 Android Studio 迁移工具
1. 打开 Android Studio
2. 选择 `Refactor` → `Migrate to AndroidX`
3. 预览迁移变更
4. 执行自动迁移

#### 2.2 更新 gradle.properties
```properties
android.useAndroidX=true
android.enableJetifier=true
```

#### 2.3 主模块依赖更新
```gradle
// 替换主要依赖
implementation 'androidx.multidex:multidex:2.0.1'
implementation 'com.google.android.material:material:1.4.0'
implementation 'androidx.appcompat:appcompat:1.3.1'
implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
implementation 'androidx.legacy:legacy-support-v4:1.0.0'

// 友盟SDK（目标依赖）
implementation 'androidx.appcompat:appcompat:1.3.1'
```

### 阶段三：子模块迁移（3-4天）

#### 3.1 commonui 模块
```gradle
implementation 'androidx.appcompat:appcompat:1.3.1'
api 'com.google.android.material:material:1.4.0'
implementation 'androidx.recyclerview:recyclerview:1.2.1'
```

#### 3.2 kf5sdkModule 模块
- 更新 `moduleSetting.gradle` 中的版本配置
- 替换所有 Support Library 引用

#### 3.3 其他子模块
- **AndroidCrop**: 验证图片裁剪功能
- **ViewPagerIndicator**: 更新到 AndroidX 兼容版本

### 阶段四：代码修改（4-6天）

#### 4.1 包名替换
```java
// 旧包名 → 新包名
android.support.v7.app.AppCompatActivity → androidx.appcompat.app.AppCompatActivity
android.support.v4.app.Fragment → androidx.fragment.app.Fragment
android.support.v7.widget.RecyclerView → androidx.recyclerview.widget.RecyclerView
android.support.design.widget.* → com.google.android.material.widget.*
```

#### 4.2 布局文件更新
```xml
<!-- 旧命名空间 -->
<android.support.v7.widget.RecyclerView />

<!-- 新命名空间 -->
<androidx.recyclerview.widget.RecyclerView />
```

#### 4.3 重点文件修改
基于代码分析，需要重点关注以下文件：
- 所有 Activity 和 Fragment 基类
- 自定义 View 组件
- 考试相关的 UI 组件
- 阅读器相关组件

### 阶段五：第三方库兼容性处理（2-N天）

#### 5.1 核心库升级计划
```gradle
// ExoPlayer
implementation 'com.google.android.exoplayer:exoplayer:2.18.1'

// Lottie
implementation 'com.airbnb.android:lottie:5.2.0'

// OkHttp
implementation 'com.squareup.okhttp3:okhttp:4.10.0'

// Gson (无需更改)
implementation 'com.google.code.gson:gson:2.8.9'
```

#### 5.2 特殊处理库    风险点--如果这几个SDK需要升级工作量就更大了
- **Polyv SDK**: 确认内部是否使用了 Support Library，当前版本 `2.18.4` 可能需要升级
- **友盟SDK**: 已确认支持 AndroidX，当前使用版本 `9.8.6` 兼容
- **微信/支付宝SDK**: 验证最新版本兼容性
  - **微信SDK**: 当前版本 `5.3.1`，确认内部是否使用了 Support Library
  - **支付宝SDK**: 确认内部是否使用了 Support Library


#### 5.3 兼容性验证清单
- [ ] 微信登录/分享功能
- [ ] 支付宝支付功能  
- [ ] Polyv 视频播放功能
- [ ] 第三方SDK初始化正常
- [ ] 相关功能回归测试

### 阶段六：测试验证与问题修改（4-5天）

#### 6.1 编译测试
- [ ] Debug 版本编译通过
- [ ] Release 版本编译通过
- [ ] 所有模块编译无错误

#### 6.2 功能测试
- [ ] 用户登录/注册
- [ ] 考试功能模块
- [ ] 文档查看功能
- [ ] 支付功能
- [ ] 直播/视频播放
- [ ] 文件上传/下载

#### 6.3 兼容性测试
- [ ] Android 5.0 (API 21) 设备测试
- [ ] Android 13 (API 33) 设备测试
- [ ] 不同屏幕尺寸适配
- [ ] 性能对比测试

#### 6.4 回归测试
- [ ] 核心业务流程测试
- [ ] UI 界面显示测试
- [ ] 第三方集成功能测试

## 风险控制

### 高风险项
1. **Polyv SDK，微信支付，支付宝支付 兼容性**: 如不支持 AndroidX，需升级sdk，版本跨度太大，可能实现接口全变了
2. **自定义组件**: 大量自定义 View 可能需要手动调整

### 风险缓解措施
1. **并行开发**: 在迁移分支进行，不影响主线开发
2. **分阶段验证**: 每个阶段完成后进行功能验证
3. **回滚方案**: 保持原有分支，必要时可快速回滚


## 成功标准

### 技术指标
- [ ] 所有模块编译通过
- [ ] 应用正常启动和运行
- [ ] 核心功能无异常
- [ ] 性能无明显下降


## 后续维护

### 长期收益
1. **技术债务清理**: 移除过时的 Support Library
2. **新特性支持**: 可使用最新的 Android 特性
3. **维护成本降低**: 减少版本冲突问题
4. **生态系统对齐**: 与 Android 官方推荐保持一致

### 维护建议
1. 定期更新 AndroidX 库版本
2. 关注官方迁移指南更新
3. 建立 AndroidX 使用规范文档


## 总结

AndroidX 迁移是一个系统性工程，需要谨慎规划和执行。通过分阶段实施、充分测试和风险控制，可以确保迁移的成功完成。迁移完成后，项目将获得更好的长期维护性和技术前瞻性。
