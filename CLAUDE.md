# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an Android educational app called "华智公考" (Huazhi Civil Service Exam), developed for civil service exam preparation in China. The app provides features like e-book reading, video learning, live streaming classes, exam practice, and social forums.

**Application ID**: `cn.huazhi.reader`  
**Package Name**: `cn.huazhi.reader`  
**Current Version**: V2.8.1 (version code 515)

## Build Configuration

### Prerequisites
- **Android Studio**: Latest version with Android Gradle Plugin 7.3.1
- **Java**: JDK 8 (sourceCompatibility/targetCompatibility VERSION_1_8)
- **Kotlin**: 1.8.0
- **Gradle**: Compatible with plugin version 7.3.1

### SDK Versions
- **compileSdkVersion**: 33
- **minSdkVersion**: 21 (Android 5.0)
- **targetSdkVersion**: 33 (Android 13)

### Common Build Commands

#### Debug Builds
```bash
# Build debug APK
./gradlew assembleDebug

# Install debug on connected device
./gradlew installDebug

# Build specific flavor debug
./gradlew assembleKaifaDebug
./gradlew assembleHuaweiDebug
```

#### Release Builds
```bash
# Build release APK
./gradlew assembleRelease

# Build specific flavor release
./gradlew assembleHuaweiRelease
./gradlew assembleOppoRelease
./gradlew assembleVivoRelease
```

#### Clean and Build
```bash
# Clean project
./gradlew clean

# Clean and rebuild
./gradlew clean assembleDebug
```

#### Lint and Code Quality
```bash
# Run lint checks
./gradlew lint

# Check dependencies
./gradlew dependencies
```

## Project Architecture

### Multi-Module Structure

The project uses a multi-module architecture with the following modules:

1. **Motoon** (Main App Module)
   - Contains all main application logic
   - Package: `cn.huazhi.reader`
   - Features: Login, exam practice, book reading, live streaming

2. **commonui** (Common UI Library)
   - Shared UI components
   - Custom views and widgets

3. **kf5sdkModule** (Customer Service SDK)
   - KF5 customer service integration
   - Contains JAR: `com.kf5sdk.framework_v2-1.6.jar`

4. **Library Modules**:
   - **StickyListHeaders**: Sticky header list views
   - **ePub-Library**: E-book reading functionality
   - **AndroidCrop**: Image cropping functionality
   - **Android-ViewPagerIndicator**: ViewPager indicators
   - **PagerSlidingTabStrip**: Sliding tab strip component

### Core Architecture Patterns

#### Package Structure (Main App)
```
cn.huazhi.reader/
├── costoon/           # Legacy naming - core app functionality
│   ├── account/       # User account management
│   ├── book/          # Book/e-book related features
│   ├── login/         # Authentication
│   ├── pay/           # Payment integration (WeChat/Alipay)
│   ├── sl/            # "申论" (Essay) related features
│   ├── system/        # System settings and utilities
│   └── video/         # Video playback
├── readerV2/          # V2 architecture - modern features
│   ├── exam/          # Exam practice and testing
│   ├── forum/         # Social forum features
│   ├── livestreaming/ # Live streaming classes
│   ├── my/            # User profile and personal data
│   ├── paper/         # Exam paper handling
│   ├── study/         # Learning modules and courses
│   └── search/        # Search functionality
├── epub/              # E-book reader implementation
├── database/          # Local database operations
├── utils/             # Utility classes
└── wxapi/             # WeChat integration
```

#### Key Design Patterns
- **MVP Pattern**: Used in V2 modules with presenter/view/model separation
- **Fragment-based UI**: Heavy use of fragments for modular UI
- **Custom Application Class**: `MotoonApplication` for app initialization
- **Database Layer**: SQLite with custom helper classes
- **Network Layer**: OkHttp 3.7.0 for API calls

## Development Flavors

The app supports multiple distribution channels with different configurations:

### Production Flavors
- **huawei**: Huawei App Store (channel: 9000001)
- **oppo**: OPPO App Store (channel: 9000002)
- **vivo**: VIVO App Store (channel: 9000003)
- **yingyongbao**: Tencent App Store (channel: 9000004)
- **xiaomi**: Xiaomi App Store (channel: 9000005)
- **rongyao**: Honor App Store (channel: 9000006)

### Development Flavors
- **kaifa**: Development build (channel: 9000000)
- **huaweifufei**: Huawei paid version (channel: 90010001)
- **oppofufei**: OPPO paid version (channel: 90010002)

All flavors use production API: `https://api.costoon.com`

## Key Third-Party Integrations

### Video and Media
- **ExoPlayer 2.9.1**: Main video player
- **Polyv SDK 2.18.4**: Live streaming and video services
- **Lottie 2.2.5**: Animations

### Social and Payment
- **WeChat SDK 5.3.1**: Login, sharing, payment
- **Alipay SDK**: Payment integration
- **Weibo SDK 3.1.4**: Social sharing
- **QQ SDK**: Social integration

### Analytics and Services
- **UMeng SDK 9.8.6**: Analytics and statistics
- **Bugly**: Crash reporting
- **KF5 SDK**: Customer service

### UI and Utilities
- **Material Design Components**
- **OkHttp 3.7.0**: Network requests
- **Gson 2.8.0**: JSON parsing
- **EventBus 3.2.0**: Event communication
- **XXPermissions 18.5**: Runtime permissions

## Important Configuration Files

### Signing Configuration
- **Debug Keystore**: `motoon.key`
- **Release Keystore**: `motoon.key` (same as debug)
- **Key Alias**: `costoon`
- **Store Password**: `sR54#D^g32*4uZX`
- **Key Password**: `s4EWs%821YG-^F2`

### ProGuard Configuration
- Main app: `Motoon/proguard-project.txt`
- Sub-modules have individual ProGuard configurations
- **Important**: Release builds have shrinkResources disabled

### Build Optimizations
- **MultiDex enabled**: For large APK support
- **Java heap size**: 4GB (`org.gradle.jvmargs=-Xmx4608M`)
- **NDK filters**: `armeabi`, `armeabi-v7a`, `arm64-v8a`

## Testing Strategy

### Test Configuration
- **Test Runner**: `android.support.test.runner.AndroidJUnitRunner`
- **Unit Tests**: Located in standard `src/test` directories
- **Instrumentation Tests**: Located in `src/androidTest` directories

### Test Dependencies (commonui module example)
```gradle
testImplementation 'junit:junit:4.12'
androidTestImplementation 'com.android.support.test:runner:1.0.2'
androidTestImplementation 'com.android.support.test.espresso:espresso-core:3.0.2'
```

### Running Tests
```bash
# Run unit tests
./gradlew test

# Run instrumentation tests
./gradlew connectedAndroidTest

# Run tests for specific module
./gradlew :commonui:test
```

## AndroidX Migration Status

**Status**: Planning phase - Not yet migrated to AndroidX

The project currently uses Android Support Library 27.1.1. A comprehensive AndroidX migration plan exists in `docs/AndroidX_Migration_Plan.md` with detailed implementation steps.

### Key Migration Challenges
- Large codebase with extensive Support Library usage
- Third-party SDK compatibility (Polyv, WeChat, Alipay)
- Multiple modules requiring coordinated migration

## Code Style and Conventions

### Resource Naming (from rename.md)
- **Drawable naming**: `shape_action_color` format
  - Shapes: `circle`, `rect`, `corners`, `cornerses`
  - Actions: `solid`, `stroke`
  - Example: `circle_stroke_red`
- **Selector naming**: `selector_` prefix
- **Color naming**: Use color values or theme names

### Development Guidelines
- Use MVP pattern for new V2 features
- Follow existing package structure conventions
- Maintain separation between V1 (`costoon`) and V2 (`readerV2`) code
- Use ViewBinding for new UI components (enabled in build.gradle)

## Common Development Tasks

### Adding New Features
1. Determine if feature belongs in V1 (`costoon`) or V2 (`readerV2`) architecture
2. Create appropriate presenter/view/model classes for V2 features
3. Add necessary permissions to AndroidManifest.xml
4. Update ProGuard rules if using reflection or third-party libraries

### Working with Live Streaming
- Uses Polyv SDK for live streaming functionality
- Video playback through ExoPlayer integration
- Custom controls in `livestreaming` package

### Payment Integration
- WeChat Pay: `cn.huazhi.reader.wxapi` package
- Alipay: `cn.huazhi.reader.costoon.pay` package
- Test payment configurations available for development

### Database Operations
- Custom database helper classes in `database` package
- Table-specific classes for different data types
- Use existing patterns for new database operations

## Debugging and Troubleshooting

### Common Issues
- **Build errors**: Often related to duplicate dependencies or version conflicts
- **NDK issues**: Ensure correct ABI filters are set
- **Memory issues**: Increase heap size in gradle.properties if needed
- **Signing issues**: Verify keystore paths and passwords

### Useful Build Properties
```properties
# gradle.properties
org.gradle.jvmargs=-Xmx4608M -XX:MaxPermSize=2048m
android.useAndroidX=false  # Not yet migrated
android.enableJetifier=false  # Not yet migrated
```

## Security Considerations

- Keystore credentials are embedded in build files (production consideration)
- API keys and secrets are configured as build config fields
- Network security config is defined in AndroidManifest.xml
- ProGuard obfuscation is applied to release builds