apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'

dependencies {
    implementation fileTree(include: ['*.aar', '*.jar'], dir: 'libs')
    implementation project(':StickyListHeaders')
    implementation project(':ePub-Library')
    implementation project(':AndroidCrop')
    implementation project(':Android-ViewPagerIndicator')
    implementation project(':PagerSlidingTabStrip')
    implementation project(':kf5sdkModule')
    implementation project(':commonui')
    implementation 'com.android.support:multidex:1.0.3'
    implementation 'com.android.support:design:27.1.1'
    implementation 'com.squareup.okhttp3:okhttp:3.7.0'
    implementation 'me.drakeet.multitype:multitype:2.5.0'
    implementation 'com.airbnb.android:lottie:2.2.5'
    implementation 'com.google.code.gson:gson:2.8.0'
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation 'org.greenrobot:eventbus:3.2.0'
    implementation 'com.binioter:guideview:1.0.0'
    implementation 'com.tencent.bugly:crashreport:latest.release'
//    api 'tv.danmaku.ijk.media:ijkplayer-java:0.8.4'
//    api 'tv.danmaku.ijk.media:ijkplayer-armv7a:0.8.4'

    implementation 'com.google.android.exoplayer:exoplayer:2.9.1'
    implementation('net.polyv.android:polyvPlayer:2.18.4') {
        exclude(module: 'alicloud-android-utdid')
    }//SDK核心包

    implementation 'net.polyv.android:polyvModuleABI:1.7.6'//SDK核心包
    implementation 'net.polyv.android:polyvDownload:2.18.4'//SDK下载功能
    implementation 'net.polyv.android:polyvUpload:2.3.3'//SDK上传功能
    implementation 'de.hdodenhof:circleimageview:2.2.0'//圆形imageview，音频 封面图使用
    implementation ('net.polyv.android:polyvGif:2.2.1') {
        exclude group: 'pl.droidsonroids.gif', module: 'android-gif-drawable'
    }

    implementation 'net.polyv.android:polyvSub:2.11.1'
    implementation 'com.android.support:appcompat-v7:27.1.1'
    implementation 'com.android.support.constraint:constraint-layout:1.1.3'
    implementation "org.jetbrains.kotlin:kotlin-reflect:1.4.0"
    implementation 'com.android.support:support-v4:27.1.1'//弹幕、截图功能中使用
    implementation 'com.github.PhilJay:MPAndroidChart:v3.0.3' //图表控件

    // 腾讯浏览服务
//    api 'com.tencent.tbs:tbssdk:44199'
    // umeng sdk
    // 友盟基础组件库（所有友盟业务SDK都依赖基础组件库）
    implementation 'com.umeng.umsdk:common:9.5.2'// (必选)
    implementation 'com.umeng.umsdk:asms:1.6.3'// 必选

    implementation 'com.umeng.umsdk:abtest:1.0.1'//使用U-App中ABTest能力，可选

    implementation 'com.umeng.umsdk:uverify:2.5.7'// 必选
    implementation 'com.umeng.umsdk:uverify-main:2.1.4'// 必选
    implementation 'com.umeng.umsdk:uverify-logger:2.1.4'// 必选
    implementation 'com.umeng.umsdk:uverify-crashshield:2.1.4'// 必选

    // 支付宝 SDK AAR 包所需的配置
    api 'com.alipay.sdk:alipaysdk-android:+@aar'

    // 微信SDK
    api 'com.tencent.mm.opensdk:wechat-sdk-android-with-mta:5.3.1'

    //pdf
    implementation 'com.github.barteksc:android-pdf-viewer:2.8.2'
    // XXPermissions
    implementation 'com.github.getActivity:XXPermissions:18.5'
}

static def getData() {
    return new Date().format("yyyyMMdd", TimeZone.getTimeZone("UTC"))
}

android {
    compileSdkVersion 33
    packagingOptions {
        exclude 'META-INF/NOTICE.txt'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/MANIFEST.MF'
    }


    lintOptions {
        checkReleaseBuilds false
        // Or, if you prefer, you can continue to check for errors in release builds,
        // but continue the build even when errors are found:
        abortOnError false

        disable 'RestrictedApi'
    }
    dexOptions {
        javaMaxHeapSize "4g"
    }
    buildFeatures {
        dataBinding = false
        viewBinding = true
    }
    defaultConfig {
        applicationId "cn.huazhi.reader"
        minSdkVersion 21
        targetSdkVersion 33
        // Enabling multidex support.
        multiDexEnabled true
        versionCode 515
        versionName "V2.8.1"

        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"

        //appid 区分复制包和主包(三方登录、分享和支付区分)
        buildConfigField 'int', 'APPID', '7'
        //区分主包、复制包和马甲包
        buildConfigField 'int', 'SUBAPPID', '0'

        //科目id
        buildConfigField 'int', 'SUBJECTID', '0'

        //论坛板块id
        buildConfigField 'int', 'POSTID', '0'

        //应用名
        resValue "string", "app_name", "华智公考"

        //包主题颜色(包括ExPagerSlidingTabStrip,actionbar)
        resValue "color", "color_subapp", "#fb5e53"

        //appid
        //请同时修改  androidmanifest.xml里面，.PayActivityd里的属性<data android:scheme="wxb4ba3c02aa476ea1"/>为新设置的appid
        buildConfigField 'String', 'WXAPPID', '\"wxe150dcfa74f55e4a\"'

        //商户号
        buildConfigField 'String', 'WXMCHID', '\"1236352902\"'

        //api密钥
        buildConfigField 'String', 'WXAPIKEY', '\"e25fcdef3d48f7c87dd19f93be1d48b3\"'

        //qq appid
        buildConfigField 'String', 'QQAPPID', '\"1104126257\"'

        //微博 appid
        buildConfigField 'String', 'WeiBoAPPID', '\"199432258\"'
        // app 类型：:0、主包  1、复制包 2、独立app
        buildConfigField 'int', 'AppType', '0'

        //主题
        buildConfigField 'int', 'THEME', '0'

        //引导页颜色
        resValue "color", "color_splash_light", "#fb5e53"
        resValue "color", "color_splash_deep", "#fb5e53"

        //极光推送key
        manifestPlaceholders = [app_key : "wxe150dcfa74f55e4a",
                                qq_appid: "tencent1106053413"]

        flavorDimensions "baimo"

        ndk {
            abiFilters "armeabi", "armeabi-v7a", "arm64-v8a"
        }
    }
    signingConfigs {
        debug {
            keyAlias 'costoon'
            keyPassword 's4EWs%821YG-^F2'
            storeFile file('../motoon.key')
            storePassword 'sR54#D^g32*4uZX'
            v1SigningEnabled true
            v2SigningEnabled true
        }

        release {
            keyAlias 'costoon'
            keyPassword 's4EWs%821YG-^F2'
            storeFile file('../motoon.key')
            storePassword 'sR54#D^g32*4uZX'
            v1SigningEnabled true
            v2SigningEnabled true
        }
    }

    buildTypes {
        debug {
            zipAlignEnabled true
            // 移除无用的resource文件
            shrinkResources false
            buildConfigField "boolean", "MOTOON_DEBUG", "true"
        }
        release {
            zipAlignEnabled true
            // 移除无用的resource文件
            shrinkResources false
            buildConfigField "boolean", "MOTOON_DEBUG", "false"
        }
    }

    //修改生成的apk名字, 打包的时候打开,不然编译会报错
//    applicationVariants.all { variant ->
//        variant.outputs.all { output ->
//            if (variant.buildType.name == 'release') {
//                getPackageApplication().outputDirectory = new File(project.rootDir.absolutePath + "/release")
//                def apkName = "${variant.flavorName}_huazhi_${variant.versionName}_code_${variant.versionCode}_${getData()}_release.apk"
//                outputFileName = apkName
//            } else {
//                getPackageApplication().outputDirectory = new File(project.rootDir.absolutePath + "/debug")
//                def apkName = "${variant.flavorName}_huazhi_${variant.versionName}_code_${variant.versionCode}_${getData()}_debug.apk"
//                outputFileName = apkName
//            }
//        }
//    }
//    tasks.whenTaskAdded {task ->
//        if (task.name.contains("ReleaseApkListingFileRedirect")) { // 过滤release
//            task.enabled = false
//        }
//    }

    productFlavors {

        huawei {
            buildConfigField "String", "CHANNEL", "\"9000001\""
            buildConfigField "String", "URL_DOMAIN", "\"https://api.costoon.com\""
        }
        huaweifufei {
            buildConfigField "String", "CHANNEL", "\"90010001\""
            buildConfigField "String", "URL_DOMAIN", "\"https://api.costoon.com\""
        }
        oppo {
            buildConfigField "String", "CHANNEL", "\"9000002\""
            buildConfigField "String", "URL_DOMAIN", "\"https://api.costoon.com\""
        }
        oppofufei {
            buildConfigField "String", "CHANNEL", "\"90010002\""
            buildConfigField "String", "URL_DOMAIN", "\"https://api.costoon.com\""
        }
        vivo {
            buildConfigField "String", "CHANNEL", "\"9000003\""
            buildConfigField "String", "URL_DOMAIN", "\"https://api.costoon.com\""
        }
        yingyongbao {
            buildConfigField "String", "CHANNEL", "\"9000004\""
            buildConfigField "String", "URL_DOMAIN", "\"https://api.costoon.com\""
        }
        xiaomi {
            buildConfigField "String", "CHANNEL", "\"9000005\""
            buildConfigField "String", "URL_DOMAIN", "\"https://api.costoon.com\""
        }
        rongyao {
            buildConfigField "String", "CHANNEL", "\"9000006\""
            buildConfigField "String", "URL_DOMAIN", "\"https://api.costoon.com\""
        }



//        ceshi {
////            buildConfigField "String", "URL_DOMAIN", "\"http://test.api.costoon.com\""
//            buildConfigField "String", "URL_DOMAIN", "\"http://wan1.mxcorp.cn\""
//        }

        kaifa {
            buildConfigField "String", "CHANNEL", "\"9000000\""
            buildConfigField "String", "URL_DOMAIN", "\"https://api.costoon.com\""
        }

//        yufabu {
//            buildConfigField "String", "URL_DOMAIN", "\"http://pre.api.costoon.com\""
//        }


    }

    sourceSets {
        main {
            manifest.srcFile 'AndroidManifest.xml'
            java.srcDirs = ['src']
            aidl.srcDirs = ['src']
            renderscript.srcDirs = ['src']
            res.srcDirs = ['res']
            jniLibs.srcDirs = ['libs']
            assets.srcDirs = ['assets']
        }
        // Move the tests to tests/java, tests/res, etc...
        androidTest.setRoot('tests')

        // Move the build types to build-types/<type>
        // For instance, build-types/debug/java, build-types/debug/AndroidManifest.xml, ...
        // This moves them out of them default location under src/<type>/... which would
        // conflict with src/ being used by the main source set.
        // Adding new build types or product flavors should be accompanied
        // by a similar customization.
        debug.setRoot('build-types/debug')
        release.setRoot('build-types/release')
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    buildFeatures {
        viewBinding true
    }
//    compileOptions {
//        targetCompatibility 1.8
//        sourceCompatibility 1.8
//        encoding "UTF-8"
//    }
}


