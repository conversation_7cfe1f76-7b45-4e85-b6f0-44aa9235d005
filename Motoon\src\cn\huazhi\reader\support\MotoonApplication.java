
package cn.huazhi.reader.support;

import static cn.huazhi.common.log.Looger.logi;

import android.app.Activity;
import android.app.AlertDialog;
import android.app.Application;
import android.content.ComponentName;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.ServiceConnection;
import android.content.SharedPreferences;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.os.Handler;
import android.os.IBinder;
import android.os.Message;
import android.support.annotation.NonNull;
import android.support.multidex.MultiDex;
import android.support.v4.app.NotificationManagerCompat;
import android.text.TextUtils;
import android.util.Log;

import com.easefun.polyv.businesssdk.vodplayer.PolyvVodSDKClient;
import com.easefun.polyv.cloudclass.config.PolyvLiveSDKClient;
import com.easefun.polyv.foundationsdk.log.PolyvCommonLog;
import com.easefun.polyv.thirdpart.blankj.utilcode.util.Utils;
import com.easefun.polyvsdk.PolyvSDKClient;
import com.google.gson.Gson;
import com.kf5.sdk.system.init.KF5SDKInitializer;
import com.tencent.bugly.crashreport.CrashReport;
import com.umeng.analytics.MobclickAgent;
import com.umeng.commonsdk.UMConfigure;
import com.umeng.commonsdk.statistics.common.DeviceConfig;

import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.Executor;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import cn.huazhi.common.J;
import cn.huazhi.common.skin.CompatResources;
import cn.huazhi.common.skin.SkinAttrParser;
import cn.huazhi.common.skin.SkinTask;
import cn.huazhi.common.skin.parser.BackgroundParser;
import cn.huazhi.common.skin.parser.CompoundDrawableParser;
import cn.huazhi.common.skin.parser.TextColorParser;
import cn.huazhi.reader.BuildConfig;
import cn.huazhi.reader.R;
import cn.huazhi.reader.costoon.interfaces.BaseActivity;
import cn.huazhi.reader.costoon.login.LoginActivity;
import cn.huazhi.reader.costoon.system.SplashActivity;
import cn.huazhi.reader.epub.model.BookmarkDatabase;
import cn.huazhi.reader.epub.model.ReaderSettingsDatabase;
import cn.huazhi.reader.json.bean.Version;
import cn.huazhi.reader.push.receiver.TickAlarmReceiver;
import cn.huazhi.reader.utils.AndroidUtil;
import cn.huazhi.reader.utils.CrashManager;
import cn.huazhi.reader.utils.FriendMessageHelper;
import cn.huazhi.reader.utils.MD5Util;
import cn.huazhi.reader.utils.MessageUtil;
import cn.huazhi.readerV2.EntryActivity;
import cn.huazhi.readerV2.ThemeConfig;
import cn.huazhi.readerV2.common.ActivityHelper;
import cn.huazhi.readerV2.common.AppInfo;
import cn.huazhi.readerV2.common.utils.DialogHelper;
import cn.huazhi.readerV2.statistics.Statistics;
import cn.huazhi.readerV2.study._C;
import okhttp3.Interceptor;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

public class MotoonApplication extends Application {
    public static final String KEY_UUID = "uuid";
    public static final String APPLICATION_NAME = "costoon";
    public static final String STUDY_VIEW_STYLE = "study_view_style";
    private static MotoonApplication sInstance;
    private List<Activity> activityList = new LinkedList<>();

    public static final String SETTING_PREF = "settingpref";
    public static final String KEY_APP_VERSION_CODE = "version_code";
    public static final String KEY_PRIVACY = "privacy";
    SharedPreferences settings;
    // 用户类型(1:游客 2.未认证用户 3:手机认证用户 4：注册用户 10:包月用户 11:包年用户 12:临时付费 13:系统预留 14:微信 15:QQ 16:微博)
    private int type;//保存用户类型 3 注册
    private String sessionID;// 保存sessionid
    private String newIP;// 切换IP
    private int userID;
    private int restTime;
    //private String advertJson;// 广告json字符串
    private String startJson;// 起始页json字符串
    private boolean isProtect;// 流量保护
    private boolean isBookProtect;// 图书保护
    private boolean isVideoProtect;// 视频保护
    private boolean isPlayProtect;// 播放保护
    private boolean isFileProtect;// 文件保护
    //    private HttpClient httpclient;
    private boolean checked;

    private Version mLatestVersion;

    private int payResult; //支付结果 1成功 2失败

    private Bitmap screenShot;//屏幕截图

    private boolean isPlaySound;
    private boolean pushset;
    private boolean isReceiveNotification;
    private boolean isNotificationSound;
    private boolean isNotificationVibrate;
    private boolean isNotificationNightMute;
    private String notificationSoundUri;


    private static AlertDialog mTipDialog; // 重新登陆提示对话框

    public int studyPlanCourseCategoryId;  // 当前学习计划的科目ID

    private EntryActivity entryActivity;//保存EntryActivity引用

    private Resources appResources;

    /** 加密秘钥 */
    private String aeskey = "VXtlHmwfS2oYm0CZ";
    /** 加密向量 */
    private String iv = "2u9gDPKdX6GyQJKU";
    /** SDK加密串，可以在点播后台获取 */
    private String config = "jP+tIGqkYmQ7//8TLmPz1nC5y5Z0OlvrZcLi819rebPgDIxYuHkQ5cLMaOv4hGY6QuPidCE3A+LXR+eOM3kw/FdLnSzBepOr789pAL9JcpERAnnzv28Uj/WAErRtxLHuNooFcBjeRTros7n4WR1jmw==";
    private boolean initPolyv = false;

    private Handler mHandler = new Handler(){
        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);
        }
    };

    public static MotoonApplication getInstance() {
        return sInstance;
    }

    @Override
    public void attachBaseContext(Context base) {
        MultiDex.install(base);
        super.attachBaseContext(base);
    }

    @Override
    public void onCreate() {
        super.onCreate();
        settings = getSharedPreferences(SETTING_PREF, MODE_PRIVATE);
        sInstance = this;

        compareVersionCode();

        registerActivityLifecycleCallbacks(new ActivityLifecycleCallbacks() {
            @Override
            public void onActivityCreated(Activity activity, Bundle savedInstanceState) {
                if (ThemeConfig.isCostoon()) return;
                if (null != SkinTask.instance().skinResources) return;

                if (null == mLatestVersion) {
                    initLastVersion(new Gson());
                }

                if (null == mLatestVersion) return;

                ThemeConfig.THEME = "A".equals(mLatestVersion.getTheme()) ? 0 : BuildConfig.THEME;
                ThemeConfig.SUBAPPID = 0 == ThemeConfig.THEME ? 0 : BuildConfig.SUBAPPID;

                if (0 == ThemeConfig.THEME && null == SkinTask.instance().skinResources) {
                    logi("MotoonApplication: onActivityCreated: 0 == ThemeConfig.THEME && null == SkinTask.instance().skinResources");
                    if (SkinTask.loadSkin4AssetsWithSync(activity, _C.FILE_SKIN_MAIN_SKIN)) {
                        SkinTask.instance().clearSkinAttrParser();
                        List<SkinAttrParser> parsers = new ArrayList<>();
                        parsers.add(new BackgroundParser());
                        parsers.add(new TextColorParser());
                        parsers.add(new CompoundDrawableParser());
                        SkinTask.instance().addSkinAttrParser(parsers);
                    }
                }
            }

            @Override
            public void onActivityStarted(Activity activity) {

            }

            @Override
            public void onActivityResumed(Activity activity) {
                logi("MotoonApplication: onActivityResumed: " + activity);
                ActivityHelper.instance().setTopActivity(activity);
                if (activity instanceof BaseActivity && !(activity instanceof SplashActivity)) {
                    Statistics.pagein(((BaseActivity) activity).getPageStatistics());
                    MobclickAgent.onResume(activity);
                    logi("MotoonApplication: onActivityPaused: 统计开始" + activity);
                }

            }

            @Override
            public void onActivityPaused(Activity activity) {
                logi("MotoonApplication: onActivityPaused: " + activity);
                ActivityHelper.instance().removeTopActivity(activity);
                if (activity instanceof BaseActivity  && !(activity instanceof SplashActivity)) {
                    Statistics.pageout(((BaseActivity) activity).getPageStatistics());
                    MobclickAgent.onPause(activity);
                    logi("MotoonApplication: onActivityPaused:统计暂停 " + activity);
                }
            }

            @Override
            public void onActivityStopped(Activity activity) {

            }

            @Override
            public void onActivitySaveInstanceState(Activity activity, Bundle outState) {

            }

            @Override
            public void onActivityDestroyed(Activity activity) {

            }
        });
        //应用开始就设置全局捕获异常器没有设置就会用系统默认的
//        CauchExceptionHandler.getInstance().setDefaultUnCachExceptionHandler();

        UMConfigure.preInit(this, "634cc8ea88ccdf4b7e4b374e", AppInfo.channel());
        UMConfigure.setLogEnabled(BuildConfig.MOTOON_DEBUG);

        logi("MotoonApplication: onCreate");
    }

    /**
     * 对比当前versionCode 是否与存储的一致
     */
    private void compareVersionCode() {
        //获取app versionCode
        int versionCode = AndroidUtil.getVersionCode(this);
        int code = settings.getInt(KEY_APP_VERSION_CODE, 0);

        if (versionCode > code){//app升级覆盖了,需要弹出隐私协议弹窗
            settings.edit().putBoolean(KEY_PRIVACY, false).apply();
            settings.edit().putInt(KEY_APP_VERSION_CODE, versionCode).apply();
        }
    }

    public void initPolyvSdk() {

        Log.d("wocao", "initPolyvSdk: " + initPolyv);

        if (initPolyv) return;
        // 初始化保利威视-云课堂
        PolyvCommonLog.setDebug(true);
        PolyvLiveSDKClient liveSDKClient = PolyvLiveSDKClient.getInstance();
        liveSDKClient.initContext(this);
        liveSDKClient.enableHttpDns(false);

        PolyvVodSDKClient client = PolyvVodSDKClient.getInstance();
        //使用SDK加密串来配置
        client.setConfig(config, aeskey, iv);

        // 初始化保利威视-云点播
        initPolyvCilent();

        initPolyv = true;
    }




    public void initPolyvCilent() {
        //网络方式取得SDK加密串，（推荐）
        //网络获取到的SDK加密串可以保存在本地SharedPreference中，下次先从本地获取
//		new LoadConfigTask().execute();
        PolyvSDKClient client = PolyvSDKClient.getInstance();
        // 打开多用户开关，设置用户id，根据学员账号进行设置
//		openMultiAccount();

        //使用SDK加密串来配置
        client.settingsWithConfigString(config, aeskey, iv);
        //初始化SDK设置
        client.initSetting(getApplicationContext());

        //默认开启了HttpDns，使用IPV4
//		client.enableHttpDns(true);
        //如果需要支持IPV6/IPV4，请开启IPV6开关。开启后自动关闭HttpDns，采用域名访问
//		client.enableIPV6(false);

    }


    void initLite() {
        sessionID = settings.getString("sessionid", "");
        userID = settings.getInt("userID", 0);
        type = settings.getInt("type", 0);
        newIP = settings.getString("switchip", "");

//        startService(new Intent(this, JNICallbackService.class)); // 确保 app crash 后可以重新启动 im service
    }

    public void initialize() {

        // 初始化KF5
        KF5SDKInitializer.init(getApplicationContext());

        //初始化blankUtil
        Utils.init(this);

        UMConfigure.init(this, "634cc8ea88ccdf4b7e4b374e", AppInfo.channel(), UMConfigure.DEVICE_TYPE_PHONE, null);
        MobclickAgent.setPageCollectionMode(MobclickAgent.PageMode.LEGACY_MANUAL);
        UMConfigure.setProcessEvent(true);
        UMConfigure.setLogEnabled(true);
        if (getUUID() == null || "".equals(getUUID())){
            UMConfigure.getOaid(this, s -> {
                    settings.edit().putString(KEY_UUID, s).apply();
                }
            );
        }


        initLite();

        Gson gson = new Gson();
        CrashManager.registerHandler();

        BookmarkDatabase.initInstance(this);
        ReaderSettingsDatabase.initInstance(this);


        isProtect = settings.getBoolean("protect", true);
        isBookProtect = isProtect;
        isVideoProtect = isProtect;
        isPlayProtect = isProtect;
        isFileProtect = isProtect;

        isPlaySound = settings.getBoolean("sound", false);
        pushset = settings.getBoolean("pushset", false);
        isReceiveNotification = settings.getBoolean("notification", true);
        isNotificationSound = settings.getBoolean("notification_sound", true);
//        isNotificationVibrate = settings.getBoolean("notification_vibrate", true);
        isNotificationVibrate = true;
        isNotificationNightMute = settings.getBoolean("notification_night_mute", false);
        notificationSoundUri = settings.getString("notification_uri", null);

        startJson = settings.getString("startjson", "");

        restTime = settings.getInt("resttime", 0);

        initLastVersion(gson);

        //bugly上报bug
        //...在这里设置strategy的属性，在bugly初始化时传入
        CrashReport.UserStrategy strategy = new CrashReport.UserStrategy(getApplicationContext());
        //上传渠道号
        strategy.setAppChannel(AppInfo.channel());

        CrashReport.initCrashReport(getApplicationContext(), "64f0c0329c", false, strategy);
        CrashReport.setUserId(String.valueOf(userID));


    }

    private void initLastVersion(Gson gson) {
        String versionString = settings.getString("version", null);
        logi("MotoonApplication: onCreate: " + versionString);
        if (versionString != null) {
            logi("MotoonApplication: onCreate: versionString != null");
            try {
                mLatestVersion = gson.fromJson(versionString, Version.class);
                logi("MotoonApplication: onCreate: mLatestVersion = gson");
            } catch (Exception e) {
                logi("MotoonApplication: onCreate: e -> " + e.getMessage());
                mLatestVersion = null;
            }
        }
    }


    public String getProtocolVersion() {
        return "1.6.2";
    }

    /**
     * 获取版本号
     *
     * @return 当前应用的版本号
     */
    public String getAppVersion() {
        return AppInfo.versionName();
    }

    public int getUserID() {
        if (0 == userID) {
            initLite();
        }
        return userID;
    }

    public String getUUID() {
        return settings.getString(KEY_UUID, null);
    }

    public String getDeviceId() {

        return UUID.randomUUID().toString();

    }

    public String getMacAddress() {
        return DeviceConfig.getMac(getApplicationContext());
    }

    public static String getApplicationName() {
        return APPLICATION_NAME;
    }

    public int getType() {
        return type;
    }

    private OkHttpClient httpClient;

    public synchronized OkHttpClient getHttpClient() {
        if (null == httpClient) {
            OkHttpClient.Builder builder = new OkHttpClient.Builder();
            builder.connectTimeout(10_000, TimeUnit.MILLISECONDS);
            if (BuildConfig.MOTOON_DEBUG) {
                builder.readTimeout(60_000, TimeUnit.MILLISECONDS);
            } else {
                builder.readTimeout(20_000, TimeUnit.MILLISECONDS);
            }
            builder.addNetworkInterceptor(new Interceptor() {
                @Override
                public Response intercept(Chain chain) throws IOException {
                    Request originalRequest = chain.request();
                    Request requestWithUserAgent = originalRequest.newBuilder()
                            .removeHeader("User-Agent")
                            .addHeader("User-Agent", AppInfo.userAgent())
                            .build();

                    return chain.proceed(requestWithUserAgent);
                }
            });
            httpClient = builder.build();
        }
        return httpClient;
    }


    public String getSessionID() {
        return sessionID;
    }

    public boolean getBookProtect() {
        return isBookProtect;
    }

    public boolean getVideoProtect() {
        return isVideoProtect;
    }

    public boolean getPlayProtect() {
        return isPlayProtect;
    }

    public boolean getFileProtect() {
        return isFileProtect;
    }

    public Version getVersion() {
        return mLatestVersion;
    }

    public boolean isChecked() {
        return checked;
    }

    public int getRestTime() {
        return restTime;
    }

    public void setUserID(int userid) {
        userID = userid;
    }

    public void setType(int Type) {
        type = Type;
    }

    public void setProtect(boolean protect) {
        isProtect = protect;
    }

    public void setBookProtect(boolean protect) {
        isBookProtect = protect;
    }

    public void setVideoProtect(boolean protect) {
        isVideoProtect = protect;
    }

    public void setPlayProtect(boolean protect) {
        isPlayProtect = protect;
    }

    public void setFileProtect(boolean protect) {
        isFileProtect = protect;
    }

    public void setSessionID(String sessionid) {
        sessionID = sessionid;
    }

    public void setIP(String ip) {
        newIP = ip;
    }

    public String getIP() {
        return newIP;
    }

    public void setVersion(Version version) {
        this.mLatestVersion = version;
    }

    public void setStartJson(String startJson) {
        this.startJson = startJson;
    }

    public void setChecked(boolean checked) {
        this.checked = checked;
    }

    public void setRestTime(int restTime) {
        this.restTime = restTime;
    }

    public Bitmap getScreenShot() {
        return screenShot;
    }

    public void setScreenShot(Bitmap screenShot) {
        this.screenShot = screenShot;
    }

    public boolean getIsPlaySound() {
        return isPlaySound;
    }

    public void setIsPlaySound(boolean isPlaySound) {
        this.isPlaySound = isPlaySound;
    }

    public boolean isNotificationSound() {
        return isNotificationSound;
    }

    public void setIsNotificationSound(boolean isNotificationSound) {
        this.isNotificationSound = isNotificationSound;
    }

    public boolean isNotificationVibrate() {
        return isNotificationVibrate;
    }

    public void setIsNotificationVibrate(boolean isNotificationVibrate) {
        this.isNotificationVibrate = true;
    }

    public String getNotificationSoundUri() {
        return notificationSoundUri;
    }

    public void setNotificationSoundUri(String notificationSoundUri) {
        this.notificationSoundUri = notificationSoundUri;
    }

    public boolean isReceiveNotification() {
        return isReceiveNotification;
    }

    public void setIsReceiveNotification(boolean isReceiveNotification) {
        this.isReceiveNotification = isReceiveNotification;
    }

    public boolean isNotificationNightMute() {
        return isNotificationNightMute;
    }

    public void setIsNotificationNightMute(boolean isNotificationNightMute) {
        this.isNotificationNightMute = isNotificationNightMute;
    }

    public int getPayResult() {
        return payResult;
    }

    public void setPayResult(int payResult) {
        this.payResult = payResult;
    }

    public void setStudyPlanCourseCategoryId(int categoryId) {
        studyPlanCourseCategoryId = categoryId;
    }

    @Override
    public void onTerminate() {
        super.onTerminate();
        if (sInstance == this)
            sInstance = null;
    }

    public void addActivity(Activity activity) {
        activityList.add(activity);
    }

    public void removeActivity(Activity activity) {
        activityList.remove(activity);
    }

    public Activity getTopActivity() {
        if (activityList != null && !activityList.isEmpty()) {
            return activityList.get(activityList.size() - 1);
        }
        return null;
    }

    public void exit() {
        for (Activity activity : activityList) {
            activity.finish();
        }
    }

    private static final int CORE_POOL_SIZE = 100;
    private static final int MAXIMUM_POOL_SIZE = Integer.MAX_VALUE;
    private static final int KEEP_ALIVE = 1;

    private static final ThreadFactory sThreadFactory = new ThreadFactory() {
        private final AtomicInteger mCount = new AtomicInteger(1);

        public Thread newThread(Runnable r) {
            return new Thread(r, "AsyncTask #" + mCount.getAndIncrement());
        }
    };

    private static final BlockingQueue<Runnable> sPoolWorkQueue = new LinkedBlockingQueue<Runnable>(
            MAXIMUM_POOL_SIZE);

    public static final Executor THREAD_POOL_EXECUTOR = new ThreadPoolExecutor(
            CORE_POOL_SIZE, MAXIMUM_POOL_SIZE, KEEP_ALIVE, TimeUnit.SECONDS,
            sPoolWorkQueue, sThreadFactory);

    private boolean isNativeLogin;

    public void setIsNativeLogin(boolean isLogin) {
        isNativeLogin = isLogin;
    }

    public boolean isNativeLogin() {
        return isNativeLogin;
    }

    public void onUserLogin() {

        MotoonApplication.getInstance().setSessionID(null);
        SharedPreferences settings = MotoonApplication.getInstance()
                .getSharedPreferences(SETTING_PREF, 0);
        SharedPreferences.Editor editor = settings.edit();
        editor.remove("sessionid");
        editor.apply();

        FriendMessageHelper.forceCheck();
        Intent broadcastIntent = new Intent(MotoonApplication.getInstance(), TickAlarmReceiver.class);
        broadcastIntent.putExtra("delay", 0);
        MotoonApplication.getInstance().sendBroadcast(broadcastIntent);

        if (null == MotoonApplication.getInstance().getTopActivity()) {
            J.toast("您的帐号在其他客户端登录，您已被迫下线。");
        } else {

            DialogHelper.onShowOutLineAppDialog(MotoonApplication.getInstance().getTopActivity(), v -> {
                startGuide();
            });
        }
    }

    private void startGuide() {
        Intent intent = new Intent(MotoonApplication.getInstance(), SplashActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        intent.putExtra(LoginActivity.KEY_LOGIN, true);
        Activity last = null;
        while (MotoonApplication.getInstance().getTopActivity() != null) {
            if (last != null) {
                last.finish();
            }
            last = MotoonApplication.getInstance().getTopActivity();
            MotoonApplication.getInstance().removeActivity(last);
        }
        if (last != null) {
            last.startActivity(intent);
            last.finish();
        }
    }

    public void saveStudyViewStyle(String card_view) {
        settings.edit().putString(STUDY_VIEW_STYLE, card_view).apply();
    }

    public String getStudyViewStyle() {
        return settings.getString(STUDY_VIEW_STYLE, "");
    }

    public void setEntryActivity(EntryActivity entryActivity) {
        this.entryActivity = entryActivity;
    }

    public EntryActivity getEntryActivity() {
        return entryActivity;
    }

    @Override
    public Resources getResources() {
        if (null == SkinTask.instance().skinResources) {
            return super.getResources();
        }
        if (null == appResources) {
            appResources = new CompatResources(super.getResources(),
                    SkinTask.instance().skinResources, SkinTask.instance().skinPackageName);
        }
        return appResources;
    }

    private boolean isServiceBound;
    private ServiceConnection conn = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            logi("MotoonApplication: onServiceConnected");
            isServiceBound = true;
        }

        @Override
        public void onServiceDisconnected(ComponentName name) {
            logi("MotoonApplication: onServiceDisconnected");
            isServiceBound = false;
        }
    };

}
